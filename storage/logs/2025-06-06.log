{"level":"fatal","timestamp":"2025-06-06T11:39:42.497Z","context":[],"errors":[{"issues":[{"code":"invalid_type","expected":"integer","received":"float","message":"Expected integer, received float","path":["dns","maxItems"]}],"name":"ZodError","message":"[\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"integer\",\n    \"received\": \"float\",\n    \"message\": \"Expected integer, received float\",\n    \"path\": [\n      \"dns\",\n      \"maxItems\"\n    ]\n  }\n]","stack":"ZodError: \n    at get error (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:39:31)\n    at ZodObject.parse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:114:22)\n    at Object.transform (/Users/<USER>/Projects/new-rpc-proxy/app/config/endpoints.ts:50:39)\n    at ZodEffects._parse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:3271:39)\n    at ZodObject._parse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:1959:37)\n    at ZodObject._parseSync (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:100:29)\n    at ZodObject.safeParse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:129:29)\n    at ZodObject.parse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:111:29)\n    at <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:5:38)","errors":[{"code":"invalid_type","expected":"integer","received":"float","path":["dns","maxItems"],"name":"Error","message":"Expected integer, received float","stack":"Error: Expected integer, received float\n    at getStack (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/stack.js:25:14)\n    at setStack (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/stack.js:13:13)\n    at objectifyError (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/create/object.js:23:1)\n    at createError (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/create/main.js:13:8)\n    at recurseException (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/main.js:22:14)\n    at normalizeException (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/main.js:9:1)\n    at mergeError (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/merge-error-cause@5.0.2/node_modules/merge-error-cause/build/src/main.js:29:14)\n    at recurse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/merge-error-cause@5.0.2/node_modules/merge-error-cause/build/src/main.js:27:29)\n    at file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/merge-error-cause@5.0.2/node_modules/merge-error-cause/build/src/aggregate.js:20:14\n    at Array.map (<anonymous>)"}]}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-06T11:47:34.570Z","context":[],"errors":[{"issues":[{"code":"invalid_type","expected":"object","received":"undefined","path":["logger"],"message":"Required"}],"name":"ZodError","message":"[\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [\n      \"logger\"\n    ],\n    \"message\": \"Required\"\n  }\n]","stack":"ZodError: \n    at get error (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:39:31)\n    at ZodObject.parse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/zod@3.25.51/node_modules/zod/dist/esm/v3/types.js:114:22)\n    at <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:5:38)","errors":[{"code":"invalid_type","expected":"object","received":"undefined","path":["logger"],"name":"Error","message":"Required","stack":"Error: Required\n    at getStack (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/stack.js:25:14)\n    at setStack (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/stack.js:13:13)\n    at objectifyError (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/create/object.js:23:1)\n    at createError (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/create/main.js:13:8)\n    at recurseException (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/main.js:22:14)\n    at normalizeException (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/normalize-exception@4.0.1/node_modules/normalize-exception/build/src/main.js:9:1)\n    at mergeError (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/merge-error-cause@5.0.2/node_modules/merge-error-cause/build/src/main.js:29:14)\n    at recurse (file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/merge-error-cause@5.0.2/node_modules/merge-error-cause/build/src/main.js:27:29)\n    at file:///Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/merge-error-cause@5.0.2/node_modules/merge-error-cause/build/src/aggregate.js:20:14\n    at Array.map (<anonymous>)"}]}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-06T12:18:09.049Z","context":[],"errors":[{"timestamp":"2025-06-06T12:18:09.043Z","request":{"id":"3","method":"OPTIONS","headers":{},"body":"","metadata":{}},"undiciResponse":{"statusCode":200,"headers":{"date":"Fri, 06 Jun 2025 12:18:09 GMT","content-type":"application/json; charset=utf-8","content-length":"0","connection":"keep-alive","server":"cloudflare","nel":"{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}","cf-ray":"94b7d9c27cb5b4cd-SIN","allow":"OPTIONS, POST","accept":"application/json","x-rpc-bravo":"normal","alt-svc":"h3=\":443\"; ma=86400","cf-cache-status":"DYNAMIC","report-to":"{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=fiE9Kj%2BgE3Rf5mYxmfw2PX24pG%2BNkOOAad09UCg6405PWLubUi1XvybO4%2FuCoMd%2FttS9YEPqQ68wQ9ZXWlCPF3f7AtMPcxWyLzhYGEdiSQyvSWZWuuDr5fhjhDwBGWJ1fCrXw7Xl\"}],\"group\":\"cf-nel\",\"max_age\":604800}","server-timing":"cfL4;desc=\"?proto=TCP&rtt=39484&min_rtt=38585&rtt_var=15112&sent=5&recv=7&lost=0&retrans=0&sent_bytes=3941&recv_bytes=1810&delivery_rate=111960&cwnd=252&unsent_bytes=0&cid=d8c06d8b0d256914&ts=722&x=0\""},"trailers":{},"opaque":null,"body":"[object Stream]"},"body":"","name":"SenderRequestError","message":"Invalid response: Response is not a JSON-RPC message\nRequest failed","stack":"SenderRequestError: \n    at Endpoint.formatResponseBody (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/base-endpoint.ts:93:19)\n    at execute (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/sender.ts:109:49)\n    at async withRetry (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/retry.ts:48:22)\n    at async Sender.processSend (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/sender.ts:115:20)\n    at async Promise.all (index 2)\n    at async Endpoint.warmup (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/endpoint.ts:16:9)\n    at async <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:6:1)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-06T12:19:06.427Z","context":[],"errors":[{"timestamp":"2025-06-06T12:19:06.421Z","request":{"id":"2","method":"OPTIONS","headers":{},"body":"","metadata":{}},"undiciResponse":{"statusCode":200,"headers":{"date":"Fri, 06 Jun 2025 12:19:06 GMT","content-type":"application/json; charset=utf-8","content-length":"0","connection":"keep-alive","server":"cloudflare","nel":"{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}","cf-ray":"94b7db290beaf408-SIN","allow":"OPTIONS, POST","accept":"application/json","x-rpc-bravo":"normal","alt-svc":"h3=\":443\"; ma=86400","cf-cache-status":"DYNAMIC","report-to":"{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=VObo2DLUzeeSJuLUvNaz5vceI4bdEwLxdieQokKvyzuZK4bLZWFecBv9yP8J49njUvTQmPN94XqNPQHc%2Be5Y4X6KLG8XlC33HwtVF1nkc4%2B9s9t%2FFX63XiihLWSeByH4CVfzf0NR\"}],\"group\":\"cf-nel\",\"max_age\":604800}","server-timing":"cfL4;desc=\"?proto=TCP&rtt=41124&min_rtt=40813&rtt_var=11756&sent=6&recv=8&lost=0&retrans=0&sent_bytes=3941&recv_bytes=1810&delivery_rate=105835&cwnd=252&unsent_bytes=0&cid=6cfb8c2f71cb9918&ts=739&x=0\""},"trailers":{},"opaque":null,"body":"[object Stream]"},"body":"","name":"SenderRequestError","message":"Invalid response: Response is not a JSON-RPC message\nRequest failed","stack":"SenderRequestError: \n    at Endpoint.formatResponseBody (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/base-endpoint.ts:93:19)\n    at execute (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/sender.ts:109:49)\n    at async withRetry (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/retry.ts:48:22)\n    at async Sender.processSend (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/sender.ts:115:20)\n    at async Promise.all (index 1)\n    at async Endpoint.warmup (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/endpoint.ts:16:9)\n    at async <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:6:1)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-06T12:22:24.968Z","context":[],"errors":[{"timestamp":"2025-06-06T12:22:24.956Z","request":{"id":"5","method":"OPTIONS","headers":{},"body":"","metadata":{}},"undiciResponse":{"statusCode":200,"headers":{"date":"Fri, 06 Jun 2025 12:22:24 GMT","content-type":"application/json; charset=utf-8","content-length":"0","connection":"keep-alive","server":"cloudflare","nel":"{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}","cf-ray":"94b7e00119d3561c-SIN","allow":"OPTIONS, POST","accept":"application/json","x-rpc":"normal","alt-svc":"h3=\":443\"; ma=86400","cf-cache-status":"DYNAMIC","report-to":"{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=rNNcHE3xrlYUm6nVN6wrcA%2BYhe68y7f5vYurB%2BhzKaQUxZuUYBspSV3%2B1za%2BvgitMV%2BY8WUyKNvaKeZDBS5BaQolVYX1geL1jKLof4nqY8mmVaavvEZijm5iqjjuScpTBi%2Bh6Yqh\"}],\"group\":\"cf-nel\",\"max_age\":604800}","server-timing":"cfL4;desc=\"?proto=TCP&rtt=40466&min_rtt=39775&rtt_var=11561&sent=6&recv=8&lost=0&retrans=0&sent_bytes=3942&recv_bytes=1810&delivery_rate=106785&cwnd=252&unsent_bytes=0&cid=45a9ec35f4dc20b0&ts=794&x=0\""},"trailers":{},"opaque":null,"body":"[object Stream]"},"response":{"id":"5","status":200,"headers":{"date":"Fri, 06 Jun 2025 12:22:24 GMT","content-type":"application/json; charset=utf-8","content-length":"0","connection":"keep-alive","server":"cloudflare","nel":"{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}","cf-ray":"94b7e00119d3561c-SIN","allow":"OPTIONS, POST","accept":"application/json","x-rpc":"normal","alt-svc":"h3=\":443\"; ma=86400","cf-cache-status":"DYNAMIC","report-to":"{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=rNNcHE3xrlYUm6nVN6wrcA%2BYhe68y7f5vYurB%2BhzKaQUxZuUYBspSV3%2B1za%2BvgitMV%2BY8WUyKNvaKeZDBS5BaQolVYX1geL1jKLof4nqY8mmVaavvEZijm5iqjjuScpTBi%2Bh6Yqh\"}],\"group\":\"cf-nel\",\"max_age\":604800}","server-timing":"cfL4;desc=\"?proto=TCP&rtt=40466&min_rtt=39775&rtt_var=11561&sent=6&recv=8&lost=0&retrans=0&sent_bytes=3942&recv_bytes=1810&delivery_rate=106785&cwnd=252&unsent_bytes=0&cid=45a9ec35f4dc20b0&ts=794&x=0\""},"metadata":{},"took":"1233951375n"},"name":"SenderRequestError","message":"Cannot use 'in' operator to search for 'error' in undefined\nRequest failed","stack":"SenderRequestError: \n    at isKeyOf (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+utils@0.0.17/node_modules/@kdt310722/src/object/object.ts:12:12)\n    at <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+utils@0.0.17/node_modules/@kdt310722/src/object/object.ts:16:32)\n    at Array.every (<anonymous>)\n    at isKeysOf (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+utils@0.0.17/node_modules/@kdt310722/src/object/object.ts:16:17)\n    at isJsonRpcErrorResponseMessage (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+rpc@0.2.1/node_modules/@kdt310722/rpc/src/utils/messages.ts:33:12)\n    at Endpoint.shouldRetryResponse (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/base-endpoint.ts:79:34)\n    at withRetry (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/retry.ts:78:45)\n    at async Sender.processSend (/Users/<USER>/Projects/new-rpc-proxy/app/utils/sender/sender.ts:115:20)\n    at async Promise.all (index 4)"}],"metadata":{}}
{"level":"error","timestamp":"2025-06-06T14:20:48.900Z","context":[],"errors":[],"metadata":{},"message":"Circuit breaker tripped for endpoint Test!"}
{"level":"fatal","timestamp":"2025-06-06T14:28:44.023Z","context":[],"errors":[{"name":"Error","message":"Unhandled rejection:","stack":"Error: Unhandled rejection:\n    at process.<anonymous> (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+logger@0.0.12_@kdt310722+utils@0.0.19/node_modules/@kdt310722/logger/src/base-logger.ts:210:19)\n    at process.emit (node:events:518:28)\n    at process.emit (/opt/homebrew/lib/node_modules/tsx/dist/suppress-warnings.cjs:1:472)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-06T14:29:50.782Z","context":[],"errors":[{"name":"Error","message":"Unhandled rejection:","stack":"Error: Unhandled rejection:\n    at process.<anonymous> (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+logger@0.0.12_@kdt310722+utils@0.0.19/node_modules/@kdt310722/logger/src/base-logger.ts:210:19)\n    at process.emit (node:events:518:28)\n    at process.emit (/opt/homebrew/lib/node_modules/tsx/dist/suppress-warnings.cjs:1:472)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"}],"metadata":{}}
